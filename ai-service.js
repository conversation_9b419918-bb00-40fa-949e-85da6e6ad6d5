const https = require('https');

class AIService {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseURL = 'https://openrouter.ai/api/v1';
  }

  setApiKey(apiKey) {
    this.apiKey = apiKey;
  }

  async sendMessage(message, model = 'openai/gpt-3.5-turbo') {
    if (!this.apiKey) {
      throw new Error('API key not set');
    }

    // Handle both string messages and message objects
    let messageContent = message;
    let messageRole = 'user';
    
    if (typeof message === 'object' && message.content) {
      messageContent = message.content;
      messageRole = message.role || 'user';
    }

    const data = JSON.stringify({
      model: model,
      messages: [
        {
          role: messageRole,
          content: messageContent
        }
      ],
      max_tokens: 1000,
      temperature: 0.7
    });

    const options = {
      hostname: 'openrouter.ai',
      port: 443,
      path: '/api/v1/chat/completions',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length,
        'Authorization': `Bearer ${this.apiKey}`,
        'HTTP-Referer': 'https://ai-chat-electron.app',
        'X-Title': 'AI Chat Electron'
      }
    };

    return new Promise((resolve, reject) => {
      const req = https.request(options, (res) => {
        let responseData = '';

        res.on('data', (chunk) => {
          responseData += chunk;
        });

        res.on('end', () => {
          try {
            const parsedData = JSON.parse(responseData);
            
            if (res.statusCode === 200) {
              const aiResponse = parsedData.choices[0]?.message?.content;
              if (aiResponse) {
                resolve(aiResponse);
              } else {
                reject(new Error('Invalid response format from AI'));
              }
            } else {
              reject(new Error(`API Error: ${parsedData.error?.message || 'Unknown error'}`));
            }
          } catch (error) {
            reject(new Error(`Failed to parse response: ${error.message}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(new Error(`Request failed: ${error.message}`));
      });

      req.write(data);
      req.end();
    });
  }

  async testConnection() {
    try {
      const response = await this.sendMessage('Hello');
      return { success: true, response };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

module.exports = AIService; 