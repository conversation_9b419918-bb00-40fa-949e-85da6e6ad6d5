# Electrobox - AI Chat Electron App

A desktop AI chat application built with Electron and React, featuring local SQLite storage and OpenRouter AI integration.

## Features

- 💬 **AI Chat Interface**: Chat with AI models through OpenRouter API
- 💾 **Local Storage**: All conversations saved locally in SQLite database
- 🔒 **Privacy**: No data sent to external servers except for AI API calls
- 🎨 **Modern UI**: Clean, responsive chat interface
- ⚙️ **Settings**: Easy API key management
- 🗑️ **Chat Management**: Clear chat history functionality

## Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- OpenRouter API key (get one at [openrouter.ai](https://openrouter.ai))

## Installation

1. **Clone or download this project**
   ```bash
   git clone <repository-url>
   cd electrobox
   ```

2. **Install dependencies**
   ```bash
   npm install
   npm run install:renderer
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

This will start both the React development server and Electron app.

## Usage

1. **First Launch**: The app will open with an empty chat interface
2. **Configure API Key**: 
   - Click the "⚙️ Settings" button in the header
   - Enter your OpenRouter API key
   - Click "Save"
3. **Start Chatting**: Type your message and press Enter or click the send button
4. **Clear Chat**: Use the "🗑️ Clear Chat" button to start fresh

## Development

### Project Structure
```
electrobox/
├── main.js              # Electron main process
├── preload.js           # Preload script for IPC
├── database.js          # SQLite database operations
├── ai-service.js        # OpenRouter AI integration
├── renderer/            # React application
│   ├── src/
│   │   ├── App.jsx      # Main React component
│   │   ├── App.css      # Styles
│   │   └── main.jsx     # React entry point
│   ├── public/          # Static assets
│   ├── vite.config.js   # Vite configuration
│   └── package.json     # React dependencies
└── package.json         # Electron dependencies
```

### Available Scripts

- `npm run dev` - Start development mode (React + Electron)
- `npm run dev:react` - Start React development server only
- `npm run dev:electron` - Start Electron only (requires React server)
- `npm run build` - Build React app for production
- `npm run dist` - Build and run production version
- `npm start` - Run Electron with built React app
- `npm run install:renderer` - Install renderer dependencies

### Database

The app uses SQLite to store:
- Chat messages (user and AI responses)
- Settings (API keys, preferences)

Database location: `~/Library/Application Support/electrobox/chat.db` (macOS)

## Configuration

### OpenRouter API

The app is configured to use OpenRouter's API with the following default model:
- Model: `openai/gpt-3.5-turbo`
- Max tokens: 1000
- Temperature: 0.7

You can modify these settings in `ai-service.js`.

## Building for Distribution

To create a distributable app:

1. **Install electron-builder**
   ```bash
   npm install --save-dev electron-builder
   ```

2. **Build the app**
   ```bash
   npm run build:electron
   ```

The built app will be available in the `dist` directory.

## Troubleshooting

### Common Issues

1. **Port 5173 already in use**: Make sure no other Vite development server is running
2. **API key not working**: Verify your OpenRouter API key is correct and has sufficient credits
3. **Database errors**: Check that the app has write permissions to the Application Support directory

### Development Tips

- Use `npm run dev:react` to start just the React server for faster development
- The Electron app will automatically reload when you make changes to the main process files
- Check the Electron DevTools for debugging renderer process issues

## License

ISC License 