const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Database operations
  saveMessage: (message) => ipcRenderer.invoke('save-message', message),
  getMessages: () => ipcRenderer.invoke('get-messages'),
  clearMessages: () => ipcRenderer.invoke('clear-messages'),
  
  // AI API operations
  sendToAI: (message) => ipcRenderer.invoke('send-to-ai', message),
  
  // Settings operations
  saveApiKey: (apiKey, name, setActive) => ipcRenderer.invoke('save-api-key', apiKey, name, setActive),
  getApiKeys: () => ipcRenderer.invoke('get-api-keys'),
  getActiveApiKey: () => ipcRenderer.invoke('get-active-api-key'),
  setActiveApiKey: (id) => ipcRenderer.invoke('set-active-api-key', id),
  deleteApiKey: (id) => ipcRenderer.invoke('delete-api-key', id),
  updateApiKeyName: (id, name) => ipcRenderer.invoke('update-api-key-name', id, name),
  getApiKey: () => ipcRenderer.invoke('get-api-key'),
  testApiKey: (apiKey) => ipcRenderer.invoke('test-api-key', apiKey),
  
  // App info
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
}); 