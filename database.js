const Database = require('better-sqlite3');
const path = require('path');
const { app } = require('electron');

class ChatDatabase {
  constructor() {
    // Get the user data directory for storing the database
    const userDataPath = app.getPath('userData');
    const dbPath = path.join(userDataPath, 'chat.db');
    
    this.db = new Database(dbPath);
    this.initDatabase();
  }

  initDatabase() {
    // Create messages table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        content TEXT NOT NULL,
        sender TEXT NOT NULL,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        conversation_id TEXT DEFAULT 'default'
      )
    `);

    // Create settings table for API keys and other config
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL
      )
    `);

    // Create API keys table for multiple API key management
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS api_keys (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        api_key TEXT NOT NULL,
        is_active BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_used DATETIME
      )
    `);

    console.log('Database initialized successfully');
  }

  // API Key management methods
  saveApiKey(name, apiKey, setActive = false) {
    if (setActive) {
      // Deactivate all other keys first
      this.db.exec('UPDATE api_keys SET is_active = 0');
    }
    
    const stmt = this.db.prepare(`
      INSERT INTO api_keys (name, api_key, is_active)
      VALUES (?, ?, ?)
    `);
    
    const result = stmt.run(name, apiKey, setActive ? 1 : 0);
    return result.lastInsertRowid;
  }

  getApiKeys() {
    const stmt = this.db.prepare(`
      SELECT id, name, api_key, is_active, created_at, last_used
      FROM api_keys 
      ORDER BY is_active DESC, created_at DESC
    `);
    
    return stmt.all();
  }

  getActiveApiKey() {
    const stmt = this.db.prepare(`
      SELECT id, name, api_key, is_active, created_at, last_used
      FROM api_keys 
      WHERE is_active = 1
      LIMIT 1
    `);
    
    return stmt.get();
  }

  setActiveApiKey(id) {
    // Deactivate all keys first
    this.db.exec('UPDATE api_keys SET is_active = 0');
    
    // Activate the selected key
    const stmt = this.db.prepare(`
      UPDATE api_keys SET is_active = 1
      WHERE id = ?
    `);
    
    return stmt.run(id);
  }

  deleteApiKey(id) {
    const stmt = this.db.prepare(`
      DELETE FROM api_keys WHERE id = ?
    `);
    
    return stmt.run(id);
  }

  updateApiKeyName(id, name) {
    const stmt = this.db.prepare(`
      UPDATE api_keys SET name = ?
      WHERE id = ?
    `);
    
    return stmt.run(name, id);
  }

  // Legacy methods for backward compatibility
  saveMessage(content, sender, conversationId = 'default') {
    const stmt = this.db.prepare(`
      INSERT INTO messages (content, sender, conversation_id)
      VALUES (?, ?, ?)
    `);
    
    const result = stmt.run(content, sender, conversationId);
    return result.lastInsertRowid;
  }

  getMessages(conversationId = 'default', limit = 100) {
    const stmt = this.db.prepare(`
      SELECT * FROM messages 
      WHERE conversation_id = ? 
      ORDER BY timestamp ASC 
      LIMIT ?
    `);
    
    return stmt.all(conversationId, limit);
  }

  clearMessages(conversationId = 'default') {
    const stmt = this.db.prepare(`
      DELETE FROM messages 
      WHERE conversation_id = ?
    `);
    
    return stmt.run(conversationId);
  }

  saveSetting(key, value) {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO settings (key, value)
      VALUES (?, ?)
    `);
    
    return stmt.run(key, value);
  }

  getSetting(key) {
    const stmt = this.db.prepare(`
      SELECT value FROM settings WHERE key = ?
    `);
    
    const result = stmt.get(key);
    return result ? result.value : null;
  }

  close() {
    if (this.db) {
      this.db.close();
    }
  }
}

module.exports = ChatDatabase; 