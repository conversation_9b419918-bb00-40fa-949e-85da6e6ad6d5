import React from 'react';
import { DialogueChoice, Character } from '../RPGChat';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { cn } from '../../lib/utils';
import { <PERSON>, Sparkles, AlertTriangle } from 'lucide-react';

interface ChoiceButtonsProps {
  choices: DialogueChoice[];
  character: Character;
  onChoiceSelect: (choice: DialogueChoice) => void;
  disabled?: boolean;
}

export const ChoiceButtons: React.FC<ChoiceButtonsProps> = ({
  choices,
  character,
  onChoiceSelect,
  disabled
}) => {
  const isChoiceAvailable = (choice: DialogueChoice): boolean => {
    if (!choice.requirements) return true;
    
    const { stat, minValue, hasItem } = choice.requirements;
    
    if (stat && minValue) {
      return character.stats[stat] >= minValue;
    }
    
    if (hasItem) {
      return character.inventory.some(item => item.name === hasItem);
    }
    
    return true;
  };

  const getRequirementText = (choice: DialogueChoice): string => {
    if (!choice.requirements) return '';
    
    const { stat, minValue, hasItem } = choice.requirements;
    
    if (stat && minValue) {
      const statName = stat.charAt(0).toUpperCase() + stat.slice(1);
      return `Requires ${statName} ${minValue}+`;
    }
    
    if (hasItem) {
      return `Requires ${hasItem}`;
    }
    
    return '';
  };

  const getStatIcon = (stat: string) => {
    const icons = {
      strength: '💪',
      charm: '💫',
      arcana: '🔮',
      luck: '🍀'
    };
    return icons[stat as keyof typeof icons] || '⭐';
  };

  if (choices.length === 0) return null;

  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Sparkles className="h-4 w-4" />
        <span>Choose your response:</span>
      </div>
      {/* 2x2 grid for options */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        {choices.map((choice) => {
          const isAvailable = isChoiceAvailable(choice);
          const requirementText = getRequirementText(choice);

          return (
            <Button
              key={choice.id}
              variant={isAvailable ? "outline" : "secondary"}
              disabled={disabled || !isAvailable}
              onClick={() => onChoiceSelect(choice)}
              className={cn(
                "h-auto p-4 text-left justify-start transition-all duration-300",
                "hover:shadow-accent hover:scale-[1.02]",
                isAvailable 
                  ? "border-border hover:border-accent hover:bg-accent/10" 
                  : "opacity-50 cursor-not-allowed",
                choice.consequence && "hover:animate-choice-glow"
              )}
            >
              <div className="flex-1 space-y-2">
                <div className="flex items-start justify-between gap-3">
                  <span className="font-medium text-base leading-tight">
                    {choice.text}
                  </span>
                  {!isAvailable && (
                    <Lock className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  )}
                </div>
                {choice.preview && (
                  <p className="text-sm text-muted-foreground italic">
                    {choice.preview}
                  </p>
                )}
                <div className="flex items-center gap-2 flex-wrap">
                  {requirementText && (
                    <Badge 
                      variant={isAvailable ? "secondary" : "destructive"}
                      className="text-xs"
                    >
                      {choice.requirements?.stat && getStatIcon(choice.requirements.stat)}
                      {requirementText}
                    </Badge>
                  )}
                  {choice.consequence && (
                    <Badge variant="outline" className="text-xs">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      {choice.consequence}
                    </Badge>
                  )}
                </div>
              </div>
            </Button>
          );
        })}
      </div>
      {/* User custom response input and buttons */}
      <div className="mt-6 flex flex-col gap-2">
        <input
          type="text"
          placeholder="Type your own response..."
          className="w-full rounded border border-border bg-background px-3 py-2 text-foreground focus:outline-none focus:ring-2 focus:ring-accent"
          disabled={disabled}
        />
        <div className="flex gap-2">
          <Button variant="secondary" className="flex-1" disabled={disabled}>Enhance Prompt</Button>
          <Button variant="outline" className="flex-1" disabled={disabled}>Generate More Options</Button>
        </div>
      </div>
    </div>
  );
};