import React, { useState, useRef, useEffect, useLayoutEffect } from 'react';
import { ChatArea } from './chat/ChatArea';
import { CharacterSheet } from './character/CharacterSheet';
import { ChoiceButtons } from './chat/ChoiceButtons';
import { SceneBackground } from './scene/SceneBackground';
import { SaveLoadPanel } from './save/SaveLoadPanel';
import { Button } from './ui/button';
import { Save, Menu, Settings, ArrowLeft } from 'lucide-react';
import { ChatParameters } from './ChatSetup';

export interface Character {
  id: string;
  name: string;
  avatar: string;
  stats: {
    strength: number;
    charm: number;
    arcana: number;
    luck: number;
  };
  inventory: InventoryItem[];
  relationships: Relationship[];
  level: number;
  experience: number;
}

export interface InventoryItem {
  id: string;
  name: string;
  type: 'weapon' | 'armor' | 'potion' | 'artifact' | 'misc';
  rarity: 'common' | 'uncommon' | 'rare' | 'legendary';
  description: string;
  effects?: string[];
}

export interface Relationship {
  characterName: string;
  level: number; // -100 to 100
  status: string; // "Hostile", "Neutral", "Friendly", "Allied"
}

export interface ChatMessage {
  id: string;
  sender: 'user' | 'ai';
  content: string;
  timestamp: number;
  characterName?: string;
  emotion?: 'neutral' | 'happy' | 'angry' | 'sad' | 'mysterious';
  choices?: DialogueChoice[];
}

export interface DialogueChoice {
  id: string;
  text: string;
  preview?: string;
  requirements?: {
    stat?: keyof Character['stats'];
    minValue?: number;
    hasItem?: string;
  };
  consequence?: string;
}

export interface GameState {
  currentScene: string;
  sceneBackground: string;
  mood: 'light' | 'neutral' | 'dark' | 'mysterious' | 'combat';
  chapter: string;
  questLog: string[];
}

// Timeline node type
interface TimelineNode {
  id: string;
  message: string;
  sender: 'user' | 'ai';
  choices?: DialogueChoice[];
  userInput?: string;
  parent?: TimelineNode | null;
  children: TimelineNode[];
  choiceId?: string; // The id of the choice that led to this node
  ref?: React.RefObject<HTMLDivElement>; // Added for measuring position
}

// Helper function to get character emoji based on class
const getCharacterEmoji = (characterClass: string): string => {
  const emojiMap: { [key: string]: string } = {
    adventurer: '🗡️',
    wizard: '🧙‍♂️',
    warrior: '⚔️',
    rogue: '🥷',
    cleric: '⛪',
    ranger: '🏹',
    bard: '🎵',
    monk: '🧘'
  };
  return emojiMap[characterClass] || '🗡️';
};

// Helper function to get scene background based on world setting
const getSceneBackground = (worldSetting: string): string => {
  const backgroundMap: { [key: string]: string } = {
    fantasy: 'forest',
    'sci-fi': 'space',
    'post-apocalyptic': 'wasteland',
    medieval: 'castle',
    cyberpunk: 'city',
    steampunk: 'factory',
    western: 'desert',
    modern: 'urban'
  };
  return backgroundMap[worldSetting] || 'forest';
};

// Helper function to create initial timeline based on parameters
const createInitialTimeline = (parameters?: ChatParameters): TimelineNode => {
  if (!parameters) {
    return {
      id: 'root',
      message: "Welcome to your adventure! What would you like to do?",
      sender: 'ai',
      choices: [
        { id: 'start', text: 'Begin my journey', preview: 'start the adventure' }
      ],
      children: [],
      parent: null
    };
  }

  const { characterName, characterClass, worldSetting, storyGenre, startingLocation, initialQuest } = parameters;
  const emoji = getCharacterEmoji(characterClass);
  
  let welcomeMessage = `Welcome, ${characterName}! You are a ${characterClass} in the ${worldSetting} world. `;
  welcomeMessage += `You find yourself at ${startingLocation}. `;
  
  if (initialQuest) {
    welcomeMessage += `Your quest: ${initialQuest}. `;
  }
  
  welcomeMessage += `What would you like to do?`;

  return {
    id: 'root',
    message: welcomeMessage,
    sender: 'ai',
    choices: [
      { id: 'explore', text: 'Explore the area', preview: 'investigate your surroundings' },
      { id: 'quest', text: 'Begin your quest', preview: 'start your main objective' },
      { id: 'interact', text: 'Look for others', preview: 'seek out NPCs or companions' },
      { id: 'prepare', text: 'Prepare yourself', preview: 'check your equipment and abilities' }
    ],
    children: [],
    parent: null
  };
};

// Helper: collect all nodes in the tree for navigation
function collectTimelineNodes(root: TimelineNode): TimelineNode[] {
  const nodes: TimelineNode[] = [];
  function traverse(node: TimelineNode) {
    nodes.push(node);
    node.children.forEach(traverse);
  }
  traverse(root);
  return nodes;
}

// Helper: get path from root to a node
function getTimelinePathToNode(node: TimelineNode): TimelineNode[] {
  const path: TimelineNode[] = [];
  let n: TimelineNode | undefined = node;
  while (n) {
    path.unshift(n);
    n = n.parent!;
  }
  return path;
}

// Helper: recursively collect node positions for line drawing (relative to container)
function collectNodePositions(node: TimelineNode, positions: { [key: string]: { x: number; y: number; width: number; height: number; parentId: string | null } }, parentId: string | null = null, containerRect: { left: number; top: number } = { left: 0, top: 0 }) {
  if (!node || !node.ref || !node.ref.current) return;
  const rect = node.ref.current.getBoundingClientRect();
  positions[node.id] = {
    x: rect.left - containerRect.left + rect.width / 2,
    y: rect.top - containerRect.top + rect.height / 2,
    width: rect.width,
    height: rect.height,
    parentId,
  };
  node.children.forEach(child => collectNodePositions(child, positions, node.id, containerRect));
}

// Helper: render the timeline as a horizontal mind map with dynamic lines
function FloatingTimelineTree({ node, currentNode, onJump, depth = 0, nodeRefs }: { node: TimelineNode, currentNode: TimelineNode, onJump: (n: TimelineNode) => void, depth?: number, nodeRefs: { [key: string]: React.RefObject<HTMLDivElement> } }) {
  const thisRef = React.useRef<HTMLDivElement>(null);
  node.ref = thisRef as React.RefObject<HTMLDivElement>;
  if (nodeRefs) nodeRefs[node.id] = thisRef as React.RefObject<HTMLDivElement>;
  const hasChildren = node.children.length > 0;
  return (
    <div style={{ display: 'flex', alignItems: 'center', position: 'relative', minHeight: 60 }}>
      <div ref={thisRef} style={{ zIndex: 2 }}>
        {node.sender === 'user' ? (
          <span className="text-xs px-2 py-1 rounded font-mono italic text-muted-foreground bg-muted/40 cursor-default select-none opacity-70 shadow">
            You: {node.message}
          </span>
        ) : (
          <button
            className={`text-xs px-2 py-1 rounded border shadow ${node === currentNode ? 'bg-accent text-accent-foreground font-bold border-accent' : 'bg-muted/30 text-muted-foreground border-border hover:bg-accent/20'} transition`}
            onClick={() => onJump(node)}
            disabled={node === currentNode}
            style={{ minWidth: 120 }}
          >
            {node.message.slice(0, 24) + (node.message.length > 24 ? '...' : '')}
          </button>
        )}
      </div>
      {hasChildren && (
        <div style={{ display: 'flex', flexDirection: 'column', marginLeft: 60, position: 'relative' }}>
          {node.children.map((child, i) => (
            <div key={child.id} style={{ marginTop: i === 0 ? 0 : 40 }}>
              <FloatingTimelineTree node={child} currentNode={currentNode} onJump={onJump} depth={depth + 1} nodeRefs={nodeRefs} />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

interface RPGChatProps {
  onBackToDirectory?: () => void;
  parameters?: ChatParameters;
}

const RPGChat: React.FC<RPGChatProps> = ({ onBackToDirectory, parameters }) => {
  // Initialize character based on parameters
  const [character, setCharacter] = useState<Character>(() => {
    if (!parameters) {
      return {
        id: '1',
        name: 'Adventurer',
        avatar: '🗡️',
        stats: {
          strength: 10,
          charm: 10,
          arcana: 10,
          luck: 10
        },
        inventory: [],
        relationships: [],
        level: 1,
        experience: 0
      };
    }

    const baseStats = 10;
    const difficulty = parameters.difficulty || 3;
    const statBonus = Math.max(0, difficulty - 3) * 2; // Higher difficulty = better starting stats

    return {
      id: Date.now().toString(),
      name: parameters.characterName,
      avatar: getCharacterEmoji(parameters.characterClass),
      stats: {
        strength: baseStats + (parameters.characterClass === 'warrior' ? 3 : 0) + statBonus,
        charm: baseStats + (parameters.characterClass === 'bard' ? 3 : 0) + statBonus,
        arcana: baseStats + (parameters.characterClass === 'wizard' ? 3 : 0) + statBonus,
        luck: baseStats + statBonus
      },
      inventory: [],
      relationships: [],
      level: 1,
      experience: 0
    };
  });

  // Initialize empty messages array
  const [messages, setMessages] = useState<ChatMessage[]>([]);

  // Initialize game state based on parameters
  const [gameState, setGameState] = useState<GameState>(() => {
    if (!parameters) {
      return {
        currentScene: 'Unknown Location',
        sceneBackground: 'forest',
        mood: 'neutral',
        chapter: 'Chapter 1: The Beginning',
        questLog: ['Explore the world']
      };
    }

    return {
      currentScene: parameters.startingLocation,
      sceneBackground: getSceneBackground(parameters.worldSetting),
      mood: parameters.storyGenre === 'horror' ? 'dark' : 
            parameters.storyGenre === 'mystery' ? 'mysterious' : 'neutral',
      chapter: 'Chapter 1: The Beginning',
      questLog: parameters.initialQuest ? [parameters.initialQuest] : ['Explore the world']
    };
  });

  const [timelineRoot] = useState<TimelineNode>(() => createInitialTimeline(parameters));
  const [currentNode, setCurrentNode] = useState<TimelineNode>(timelineRoot);
  const [isTyping, setIsTyping] = useState(false);
  const [showSavePanel, setShowSavePanel] = useState(false);
  const [showCharacterSheet, setShowCharacterSheet] = useState(true);
  const chatAreaRef = useRef<HTMLDivElement>(null);
  // Collapsible timeline state
  const [timelineOpen, setTimelineOpen] = useState(true);
  const [_, forceUpdate] = useState(0); // for rerendering after tree mutation
  const [svgLines, setSvgLines] = useState<{ x1: number; y1: number; x2: number; y2: number }[]>([]);
  const timelineContainerRef = useRef<HTMLDivElement>(null);
  const nodeRefs: { [key: string]: React.RefObject<HTMLDivElement> } = {};

  // All nodes in the tree (for navigation)
  const allTimelineNodes = collectTimelineNodes(timelineRoot);

  // Handle choice selection (branching)
  const handleChoiceSelect = async (choice: DialogueChoice) => {
    setIsTyping(true);
    setTimeout(() => {
      // Check if a user node for this choice already exists as a child of currentNode
      let userNode = currentNode.children.find(
        (child) => child.sender === 'user' && child.message === choice.text
      );
      if (!userNode) {
        // Create user node
        userNode = {
          id: Date.now().toString() + '-user',
          message: choice.text,
          sender: 'user',
          parent: currentNode,
          children: [],
          choiceId: choice.id
        };
        currentNode.children.push(userNode);
        // Create AI response node
        const aiNode: TimelineNode = {
          id: Date.now().toString() + '-ai',
          message: generateAIResponse(choice, parameters),
          sender: 'ai',
          choices: generateNewChoices(choice.id, parameters),
          parent: userNode,
          children: []
        };
        userNode.children.push(aiNode);
        setCurrentNode(aiNode);
      } else {
        // If already exists, jump to the existing branch
        setCurrentNode(userNode.children[0]);
      }
      setIsTyping(false);
      forceUpdate((n) => n + 1); // force rerender to update tree
    }, 800);
  };

  const generateAIResponse = (choice: DialogueChoice, parameters?: ChatParameters): string => {
    // Generate contextual responses based on the choice and world parameters
    const responses: { [key: string]: string } = {
      explore: "You take a moment to observe your surroundings. The area reveals its secrets to you...",
      quest: "You steel yourself for the journey ahead. Your quest awaits...",
      interact: "You look around for signs of other life or civilization...",
      prepare: "You check your equipment and abilities, ensuring you're ready for whatever comes next...",
      start: "Your adventure begins! The world opens up before you..."
    };

    return responses[choice.id] || "Your choice leads you forward in your journey...";
  };

  const generateNewChoices = (previousChoiceId: string, parameters?: ChatParameters): DialogueChoice[] => {
    // Generate contextual choices based on the previous choice and world parameters
    const choiceMap: { [key: string]: DialogueChoice[] } = {
      explore: [
        { id: 'investigate', text: 'Investigate further', preview: 'look deeper into the area' },
        { id: 'move', text: 'Move to a new area', preview: 'explore elsewhere' },
        { id: 'rest', text: 'Take a moment to rest', preview: 'recover and plan' }
      ],
      quest: [
        { id: 'advance', text: 'Advance toward your goal', preview: 'make progress on your quest' },
        { id: 'gather', text: 'Gather information', preview: 'learn more about your objective' },
        { id: 'prepare', text: 'Prepare for challenges', preview: 'get ready for what lies ahead' }
      ],
      interact: [
        { id: 'approach', text: 'Approach any people you see', preview: 'make contact with NPCs' },
        { id: 'observe', text: 'Observe from a distance', preview: 'watch and learn' },
        { id: 'signal', text: 'Signal for help', preview: 'try to attract attention' }
      ],
      prepare: [
        { id: 'equipment', text: 'Check your equipment', preview: 'review your gear' },
        { id: 'abilities', text: 'Review your abilities', preview: 'consider your skills' },
        { id: 'plan', text: 'Make a plan', preview: 'think about your next steps' }
      ],
      start: [
        { id: 'explore', text: 'Explore the area', preview: 'investigate your surroundings' },
        { id: 'quest', text: 'Begin your quest', preview: 'start your main objective' },
        { id: 'interact', text: 'Look for others', preview: 'seek out NPCs or companions' }
      ]
    };

    return choiceMap[previousChoiceId] || [
      { id: 'continue', text: 'Continue your journey', preview: 'move forward' }
    ];
  };

  const updateCharacterProgress = (choiceId: string) => {
    setCharacter(prev => ({
      ...prev,
      experience: prev.experience + 10,
      stats: {
        ...prev.stats,
        // Add small stat improvements based on choices
        strength: choiceId === 'advance' ? prev.stats.strength + 1 : prev.stats.strength,
        charm: choiceId === 'approach' ? prev.stats.charm + 1 : prev.stats.charm,
        arcana: choiceId === 'investigate' ? prev.stats.arcana + 1 : prev.stats.arcana,
        luck: choiceId === 'signal' ? prev.stats.luck + 1 : prev.stats.luck
      }
    }));
  };

  const handleSaveGame = () => {
    const saveData = {
      character,
      messages,
      gameState,
      timestamp: Date.now()
    };
    localStorage.setItem('rpgChatSave', JSON.stringify(saveData));
    console.log('Game saved!');
  };

  const handleLoadGame = () => {
    const savedData = localStorage.getItem('rpgChatSave');
    if (savedData) {
      const parsed = JSON.parse(savedData);
      setCharacter(parsed.character);
      setMessages(parsed.messages);
      setGameState(parsed.gameState);
      console.log('Game loaded!');
    }
  };

  useEffect(() => {
    if (chatAreaRef.current) {
      chatAreaRef.current.scrollTop = chatAreaRef.current.scrollHeight;
    }
  }, [messages]);

  // Handle timeline navigation (jump to any node)
  const handleJumpToNode = (node: TimelineNode) => {
    setCurrentNode(node);
  };

  // Timeline path for breadcrumb
  const timelinePath = getTimelinePathToNode(currentNode);

  // After render, measure node positions and set SVG lines
  useLayoutEffect(() => {
    if (!timelineOpen || !timelineContainerRef.current) return;
    const containerRect = timelineContainerRef.current.getBoundingClientRect();
    const positions: { [key: string]: { x: number; y: number; width: number; height: number; parentId: string | null } } = {};
    collectNodePositions(timelineRoot, positions, null, containerRect);
    const lines: { x1: number; y1: number; x2: number; y2: number }[] = [];
    Object.values(positions).forEach(pos => {
      if (pos.parentId && positions[pos.parentId]) {
        const parent = positions[pos.parentId];
        lines.push({
          x1: parent.x + parent.width / 2,
          y1: parent.y,
          x2: pos.x - pos.width / 2,
          y2: pos.y,
        });
      }
    });
    setSvgLines(lines);
  }, [timelineRoot, currentNode, timelineOpen]);

  return (
    <div className="min-h-screen bg-gradient-background flex overflow-x-hidden">
      <div className="flex-1 flex flex-col overflow-x-hidden">
        {/* Top Bar */}
        <div className="h-16 bg-card border-b border-border flex items-center justify-between px-4 shadow-accent">
          <div className="flex items-center gap-3">
            {/* Back to Directory Button */}
            <Button
              variant="secondary"
              size="sm"
              className="mr-2"
              onClick={onBackToDirectory || (() => window.location.reload())}
              title="Back to Main Screen"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Button>
            <div>
              <h2 className="font-bold text-foreground">{gameState.currentScene}</h2>
              <p className="text-sm text-muted-foreground">{gameState.chapter}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setTimelineOpen((open) => !open)}
              title={timelineOpen ? 'Collapse timeline' : 'Expand timeline'}
            >
              {timelineOpen ? 'Hide Timeline' : 'Show Timeline'}
            </Button>
            <Button 
              variant="outline" 
              size="sm"
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Collapsible Timeline Breadcrumb */}
        {timelineOpen && (
          <div className="w-full overflow-x-auto overflow-y-visible px-0 py-2 bg-background/80 border-b border-border" style={{ maxHeight: 420, position: 'relative' }} ref={timelineContainerRef}>
            {/* SVG lines overlay */}
            <svg style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', pointerEvents: 'none', zIndex: 1 }}>
              {svgLines.map((line, i) => (
                <line key={i} x1={line.x1} y1={line.y1} x2={line.x2} y2={line.y2} stroke="#888" strokeWidth={1.5} />
              ))}
            </svg>
            <div className="w-max" style={{ minHeight: 400, display: 'flex', justifyContent: 'center', alignItems: 'center', position: 'relative', zIndex: 2 }}>
              <FloatingTimelineTree node={timelineRoot} currentNode={currentNode} onJump={handleJumpToNode} nodeRefs={nodeRefs} />
            </div>
          </div>
        )}

        {/* Scene Background & Main Text */}
        <div className="flex-1 relative overflow-hidden flex flex-col items-center justify-center">
          <SceneBackground scene={gameState.sceneBackground} mood={gameState.mood} />
          <div className="relative z-10 w-full flex flex-col items-center justify-center h-full">
            {/* Centered main text */}
            <div className="w-full flex flex-col items-center justify-center mb-8">
              <p className="text-2xl md:text-3xl font-semibold text-center text-foreground max-w-2xl bg-background/80 rounded-xl px-8 py-6 shadow-xl">
                {currentNode.message}
              </p>
            </div>
            {/* Choices and input (only if AI node) */}
            {currentNode.sender === 'ai' && (
              <div className="w-full max-w-2xl">
                <ChoiceButtons 
                  choices={currentNode.choices || []}
                  character={character}
                  onChoiceSelect={handleChoiceSelect}
                  disabled={isTyping}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RPGChat;