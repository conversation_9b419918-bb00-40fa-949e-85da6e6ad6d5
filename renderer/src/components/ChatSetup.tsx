import React, { useState } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Slider } from './ui/slider';
import { Textarea } from './ui/textarea';
import { ArrowLeft } from 'lucide-react';

export interface ChatParameters {
  characterName: string;
  characterClass: string;
  worldSetting: string;
  storyGenre: string;
  difficulty: number;
  characterDescription: string;
  startingLocation: string;
  initialQuest: string;
}

interface ChatSetupProps {
  onStartChat: (parameters: ChatParameters) => void;
  onBack: () => void;
}

const ChatSetup: React.FC<ChatSetupProps> = ({ onStartChat, onBack }) => {
  const [parameters, setParameters] = useState<ChatParameters>({
    characterName: '',
    characterClass: 'adventurer',
    worldSetting: 'fantasy',
    storyGenre: 'adventure',
    difficulty: 3,
    characterDescription: '',
    startingLocation: '',
    initialQuest: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (parameters.characterName.trim() && parameters.startingLocation.trim()) {
      onStartChat(parameters);
    }
  };

  const characterClasses = [
    { value: 'adventurer', label: 'Adventurer', emoji: '🗡️' },
    { value: 'wizard', label: 'Wizard', emoji: '🧙‍♂️' },
    { value: 'warrior', label: 'Warrior', emoji: '⚔️' },
    { value: 'rogue', label: 'Rogue', emoji: '🥷' },
    { value: 'cleric', label: 'Cleric', emoji: '⛪' },
    { value: 'ranger', label: 'Ranger', emoji: '🏹' },
    { value: 'bard', label: 'Bard', emoji: '🎵' },
    { value: 'monk', label: 'Monk', emoji: '🧘' }
  ];

  const worldSettings = [
    { value: 'fantasy', label: 'Fantasy Realm' },
    { value: 'sci-fi', label: 'Sci-Fi Universe' },
    { value: 'post-apocalyptic', label: 'Post-Apocalyptic' },
    { value: 'medieval', label: 'Medieval Kingdom' },
    { value: 'cyberpunk', label: 'Cyberpunk City' },
    { value: 'steampunk', label: 'Steampunk World' },
    { value: 'western', label: 'Wild West' },
    { value: 'modern', label: 'Modern Day' }
  ];

  const storyGenres = [
    { value: 'adventure', label: 'Adventure' },
    { value: 'mystery', label: 'Mystery' },
    { value: 'romance', label: 'Romance' },
    { value: 'horror', label: 'Horror' },
    { value: 'comedy', label: 'Comedy' },
    { value: 'drama', label: 'Drama' },
    { value: 'thriller', label: 'Thriller' },
    { value: 'epic', label: 'Epic Quest' }
  ];

  return (
    <div className="min-h-screen bg-gradient-background p-8">
      <div className="max-w-2xl mx-auto">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="sm" onClick={onBack} className="mr-4">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold text-foreground">Create New Adventure</h1>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Character Creation</CardTitle>
              <CardDescription>Define your character's basic information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="characterName">Character Name</Label>
                  <Input
                    id="characterName"
                    value={parameters.characterName}
                    onChange={(e) => setParameters(prev => ({ ...prev, characterName: e.target.value }))}
                    placeholder="Enter your character's name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="characterClass">Character Class</Label>
                  <Select value={parameters.characterClass} onValueChange={(value) => setParameters(prev => ({ ...prev, characterClass: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {characterClasses.map((cls) => (
                        <SelectItem key={cls.value} value={cls.value}>
                          <span className="mr-2">{cls.emoji}</span>
                          {cls.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="characterDescription">Character Description</Label>
                <Textarea
                  id="characterDescription"
                  value={parameters.characterDescription}
                  onChange={(e) => setParameters(prev => ({ ...prev, characterDescription: e.target.value }))}
                  placeholder="Describe your character's appearance, personality, and background..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>World & Story</CardTitle>
              <CardDescription>Set the stage for your adventure</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="worldSetting">World Setting</Label>
                  <Select value={parameters.worldSetting} onValueChange={(value) => setParameters(prev => ({ ...prev, worldSetting: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {worldSettings.map((setting) => (
                        <SelectItem key={setting.value} value={setting.value}>
                          {setting.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="storyGenre">Story Genre</Label>
                  <Select value={parameters.storyGenre} onValueChange={(value) => setParameters(prev => ({ ...prev, storyGenre: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {storyGenres.map((genre) => (
                        <SelectItem key={genre.value} value={genre.value}>
                          {genre.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="startingLocation">Starting Location</Label>
                <Input
                  id="startingLocation"
                  value={parameters.startingLocation}
                  onChange={(e) => setParameters(prev => ({ ...prev, startingLocation: e.target.value }))}
                  placeholder="e.g., The Mystical Forest, Space Station Alpha, etc."
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="initialQuest">Initial Quest</Label>
                <Input
                  id="initialQuest"
                  value={parameters.initialQuest}
                  onChange={(e) => setParameters(prev => ({ ...prev, initialQuest: e.target.value }))}
                  placeholder="e.g., Find the lost artifact, Solve the mystery, etc."
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Difficulty</CardTitle>
              <CardDescription>How challenging should your adventure be?</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>Easy</span>
                  <span>Hard</span>
                </div>
                <Slider
                  value={[parameters.difficulty]}
                  onValueChange={(value) => setParameters(prev => ({ ...prev, difficulty: value[0] }))}
                  max={5}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="text-center text-sm font-medium">
                  Level {parameters.difficulty} - {parameters.difficulty <= 2 ? 'Easy' : parameters.difficulty <= 3 ? 'Medium' : 'Hard'}
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-4">
            <Button type="button" variant="outline" onClick={onBack}>
              Cancel
            </Button>
            <Button type="submit" disabled={!parameters.characterName.trim() || !parameters.startingLocation.trim()}>
              Start Adventure
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ChatSetup; 