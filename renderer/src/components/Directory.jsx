import React, { useState, useEffect } from 'react';
import RPGChat from './RPGChat';
import ChatSetup from './ChatSetup';
import ApiKeySetup from './ApiKeySetup';
import { Button } from './ui/button';
import { Settings } from 'lucide-react';

const getSavedChats = () => {
  const saved = localStorage.getItem('rpgChatSaves');
  return saved ? JSON.parse(saved) : [];
};

const Directory = () => {
  const [chats, setChats] = useState([]);
  const [selectedChat, setSelectedChat] = useState(null);
  const [showSetup, setShowSetup] = useState(false);
  const [showApiKeySetup, setShowApiKeySetup] = useState(false);
  const [chatParameters, setChatParameters] = useState(null);
  const [isNewChatFlow, setIsNewChatFlow] = useState(false);

  useEffect(() => {
    setChats(getSavedChats());
  }, []);

  const handleApiKeySet = () => {
    setShowApiKeySetup(false);
    if (isNewChatFlow) {
      setShowSetup(true);
    }
    // If not in new chat flow, just close the modal and stay on directory
  };

  const handleChangeApiKey = () => {
    setIsNewChatFlow(false);
    setShowApiKeySetup(true);
  };

  const handleNewChat = () => {
    setIsNewChatFlow(true);
    setShowApiKeySetup(true);
  };

  const handleStartChat = (parameters) => {
    setChatParameters(parameters);
    setShowSetup(false);
    setSelectedChat({ isNew: true, parameters });
  };

  const handleOpenChat = (chat) => {
    // Set the current save as the active one for RPGChat to load
    localStorage.setItem('rpgChatSave', JSON.stringify(chat.data));
    setSelectedChat(chat);
  };

  const handleBackToDirectory = () => {
    setSelectedChat(null);
    setShowSetup(false);
    setShowApiKeySetup(false);
    setChatParameters(null);
    setIsNewChatFlow(false);
  };

  if (showApiKeySetup) {
    return <ApiKeySetup onApiKeySet={handleApiKeySet} onBack={handleBackToDirectory} />;
  }

  if (showSetup) {
    return <ChatSetup onStartChat={handleStartChat} onBack={handleBackToDirectory} />;
  }

  if (selectedChat) {
    // Only pass parameters for new chats, not for saved chats
    const parameters = selectedChat.isNew ? chatParameters : undefined;
    return <RPGChat key={selectedChat.id || 'new'} parameters={parameters} onBackToDirectory={handleBackToDirectory} />;
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-background p-8">
      <div className="flex items-center justify-between w-full max-w-md mb-6">
        <h1 className="text-3xl font-bold text-foreground">RPG Chat Directory</h1>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={handleChangeApiKey}
          className="ml-4"
        >
          <Settings className="h-4 w-4 mr-2" />
          API Keys
        </Button>
      </div>
      <Button onClick={handleNewChat} className="mb-6">+ New Chat</Button>
      <div className="w-full max-w-md bg-card rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-semibold mb-4 text-foreground">Saved Chats</h2>
        {chats.length === 0 ? (
          <p className="text-muted-foreground">No saved chats found.</p>
        ) : (
          <ul className="space-y-3">
            {chats.map(chat => (
              <li key={chat.id} className="flex items-center justify-between bg-background rounded p-3 border border-border">
                <div>
                  <div className="font-medium text-foreground">{chat.name}</div>
                  <div className="text-xs text-muted-foreground">{new Date(chat.timestamp).toLocaleString()}</div>
                  <div className="text-xs text-muted-foreground">{chat.chapter} | Level {chat.characterLevel} {chat.characterName}</div>
                </div>
                <Button size="sm" variant="outline" onClick={() => handleOpenChat(chat)}>
                  Open
                </Button>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

export default Directory; 
