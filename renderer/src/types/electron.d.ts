export interface ElectronAPI {
  saveMessage: (message: any) => Promise<any>;
  getMessages: () => Promise<any>;
  clearMessages: () => Promise<any>;
  sendToAI: (message: any) => Promise<any>;
  saveApiKey: (apiKey: string, name?: string, setActive?: boolean) => Promise<any>;
  getApiKeys: () => Promise<any>;
  getActiveApiKey: () => Promise<any>;
  setActiveApiKey: (id: number) => Promise<any>;
  deleteApiKey: (id: number) => Promise<any>;
  updateApiKeyName: (id: number, name: string) => Promise<any>;
  getApiKey: () => Promise<any>;
  testApiKey: (apiKey: string) => Promise<any>;
  getAppVersion: () => Promise<string>;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
} 