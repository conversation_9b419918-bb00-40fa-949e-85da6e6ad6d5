const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development';

// Add electron-updater for auto-update
const { autoUpdater } = require('electron-updater');

// Import our modules
const ChatDatabase = require('./database');
const AIService = require('./ai-service');

let mainWindow;
let database;
let aiService;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets/icon.png'), // Optional: add an icon
    titleBarStyle: 'default',
    show: false // Don't show until ready
  });

  // Load the app
  if (isDev) {
    // In development, load from React dev server
    mainWindow.loadURL('http://localhost:5173');
    // Open DevTools in development
    mainWindow.webContents.openDevTools();
  } else {
    // In production, load the built React app
    mainWindow.loadFile(path.join(__dirname, 'renderer/dist/index.html'));
  }

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// Initialize services
function initializeServices() {
  database = new ChatDatabase();
  aiService = new AIService();
  
  // Load saved API key from new structure
  const activeApiKey = database.getActiveApiKey();
  if (activeApiKey) {
    aiService.setApiKey(activeApiKey.api_key);
  }
}

// This method will be called when Electron has finished initialization
app.whenReady().then(() => {
  initializeServices();
  createWindow();
  // Check for updates in production
  if (!isDev) {
    autoUpdater.checkForUpdatesAndNotify();
  }
});

// Quit when all windows are closed
app.on('window-all-closed', () => {
  // On macOS it is common for applications and their menu bar
  // to stay active until the user quits explicitly with Cmd + Q
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // On macOS it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Clean up on app quit
app.on('before-quit', () => {
  if (database) {
    database.close();
  }
});

// IPC handlers for database operations
ipcMain.handle('save-message', async (event, message) => {
  try {
    const id = database.saveMessage(message.content, message.sender, message.conversationId);
    return { success: true, id };
  } catch (error) {
    console.error('Error saving message:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-messages', async (event, conversationId = 'default') => {
  try {
    const messages = database.getMessages(conversationId);
    return { success: true, messages };
  } catch (error) {
    console.error('Error getting messages:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('clear-messages', async (event, conversationId = 'default') => {
  try {
    database.clearMessages(conversationId);
    return { success: true };
  } catch (error) {
    console.error('Error clearing messages:', error);
    return { success: false, error: error.message };
  }
});

// IPC handlers for AI operations
ipcMain.handle('send-to-ai', async (event, message) => {
  try {
    const response = await aiService.sendMessage(message);
    return { success: true, response };
  } catch (error) {
    console.error('Error sending to AI:', error);
    return { success: false, error: error.message };
  }
});

// IPC handlers for settings
ipcMain.handle('save-api-key', async (event, apiKey, name = 'Default', setActive = true) => {
  try {
    const id = database.saveApiKey(name, apiKey, setActive);
    if (setActive) {
      aiService.setApiKey(apiKey);
    }
    return { success: true, id };
  } catch (error) {
    console.error('Error saving API key:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-api-keys', async (event) => {
  try {
    const apiKeys = database.getApiKeys();
    return { success: true, apiKeys };
  } catch (error) {
    console.error('Error getting API keys:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-active-api-key', async (event) => {
  try {
    const activeApiKey = database.getActiveApiKey();
    return { success: true, apiKey: activeApiKey };
  } catch (error) {
    console.error('Error getting active API key:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('set-active-api-key', async (event, id) => {
  try {
    database.setActiveApiKey(id);
    const activeApiKey = database.getActiveApiKey();
    if (activeApiKey) {
      aiService.setApiKey(activeApiKey.api_key);
    }
    return { success: true };
  } catch (error) {
    console.error('Error setting active API key:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('delete-api-key', async (event, id) => {
  try {
    database.deleteApiKey(id);
    return { success: true };
  } catch (error) {
    console.error('Error deleting API key:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('update-api-key-name', async (event, id, name) => {
  try {
    database.updateApiKeyName(id, name);
    return { success: true };
  } catch (error) {
    console.error('Error updating API key name:', error);
    return { success: false, error: error.message };
  }
});

// Legacy handler for backward compatibility
ipcMain.handle('get-api-key', async (event) => {
  try {
    const activeApiKey = database.getActiveApiKey();
    return { success: true, apiKey: activeApiKey ? activeApiKey.api_key : null };
  } catch (error) {
    console.error('Error getting API key:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('test-api-key', async (event, apiKey) => {
  try {
    // Temporarily set the API key for testing
    const originalApiKey = aiService.apiKey;
    aiService.setApiKey(apiKey);
    
    const result = await aiService.testConnection();
    
    // Restore original API key if test fails
    if (!result.success) {
      aiService.setApiKey(originalApiKey);
    }
    
    return result;
  } catch (error) {
    console.error('Error testing API key:', error);
    return { success: false, error: error.message };
  }
});

// IPC handler for app info
ipcMain.handle('get-app-version', async (event) => {
  return app.getVersion();
}); 
